2025-08-31 23:34:02.715 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-31 23:34:02.716 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-31 23:34:02.718 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-31 23:34:02.718 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-31 23:34:02.719 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-31 23:34:02.720 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-31 23:34:05.977 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-31 23:34:05.978 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-31 23:34:05.979 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-31 23:34:05.979 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-31 23:34:05.980 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-31 23:34:05.981 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-08-31 23:34:05.982 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 23:34:05.982 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 23:34:05.983 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 23:34:05.984 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 23:34:05.985 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-31 23:34:05.995 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-31 23:34:05.998 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-31 23:34:06.000 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 23:34:06.008 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-31 23:34:06.011 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-08-31 23:34:06.013 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-31 23:34:06.013 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-31 23:34:06.014 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 23:34:06.015 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-31 23:34:06.016 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-31 23:34:06.017 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-31 23:34:06.018 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-31 23:34:06.018 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11899 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-31 23:34:06.019 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 23:34:06.034 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11754 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-31 23:34:06.035 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11792 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-31 23:34:06.231 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-31 23:34:06.231 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-31 23:34:06.235 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-31 23:34:06.237 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-31 23:34:06.238 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-31 23:34:06.239 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 23:34:06.240 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 23:34:06.241 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-08-31 23:34:06.245 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:153 | 使用已存在的配置文件: state/data/field_mappings.json
2025-08-31 23:34:06.251 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-31 23:34:06.252 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-31 23:34:06.253 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-08-31 23:34:06.254 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-31 23:34:06.254 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-08-31 23:34:06.255 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 23:34:06.256 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 23:34:06.256 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-31 23:34:06.257 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 22.4ms
2025-08-31 23:34:06.286 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-31 23:34:06.287 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-31 23:34:06.292 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-31 23:34:06.293 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-31 23:34:06.294 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-31 23:34:06.295 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-08-31 23:34:06.296 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-08-31 23:34:06.297 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-31 23:34:06.298 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-08-31 23:34:06.298 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-31 23:34:06.300 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-31 23:34:06.325 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-31 23:34:06.628 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-08-31 23:34:06.630 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-08-31 23:34:06.634 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-31 23:34:06.635 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-08-31 23:34:06.635 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-08-31 23:34:06.636 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-31 23:34:06.640 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-31 23:34:06.641 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-08-31 23:34:06.642 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-08-31 23:34:06.666 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 23:34:06.667 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 23:34:06.668 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 23:34:06.675 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-31 23:34:06.678 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-31 23:34:06.683 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-31 23:34:06.698 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 23:34:06.698 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-31 23:34:06.707 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1835 | 🏠 使用示例数据结构加载导航（首次启动或暂无数据时显示）
2025-08-31 23:34:06.714 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-31 23:34:06.715 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-31 23:34:06.717 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 4个展开项
2025-08-31 23:34:06.719 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-31 23:34:06.727 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-31 23:34:06.728 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-08-31 23:34:06.728 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-31 23:34:06.732 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-31 23:34:06.739 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 23:34:06.740 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1992 | 元数据项 salary_data_2025_05 缺少年份或月份信息，已跳过
2025-08-31 23:34:06.744 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-08-31 23:34:06.748 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-31 23:34:06.759 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年']
2025-08-31 23:34:06.762 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年']
2025-08-31 23:34:06.768 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-31 23:34:06.769 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-31 23:34:06.770 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 23:34:06.774 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 23:34:06.774 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 23:34:06.775 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1601 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-31 23:34:06.776 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1405 | 🔧 [P1-2修复] 未找到最新工资数据路径（可能是首次启动）
2025-08-31 23:34:07.050 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-31 23:34:07.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-31 23:34:07.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-08-31 23:34:07.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-08-31 23:34:07.087 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-08-31 23:34:07.088 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-31 23:34:07.092 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-31 23:34:07.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-08-31 23:34:07.095 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-31 23:34:07.097 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-08-31 23:34:07.100 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-31 23:34:07.107 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-31 23:34:07.122 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-31 23:34:07.122 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-31 23:34:07.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-31 23:34:07.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-31 23:34:07.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-31 23:34:07.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-08-31 23:34:07.136 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-31 23:34:07.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-31 23:34:07.137 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-08-31 23:34:07.138 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-31 23:34:07.141 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-31 23:34:07.141 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 23:34:07.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 23:34:07.143 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 23:34:07.166 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-08-31 23:34:07.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-08-31 23:34:07.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-08-31 23:34:07.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 23:34:07.177 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-08-31 23:34:07.181 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-31 23:34:07.182 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 23:34:07.197 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 23:34:07.197 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 23:34:07.198 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 23:34:07.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 23:34:07.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-08-31 23:34:07.206 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-31 23:34:07.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 51.7ms
2025-08-31 23:34:07.209 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 23:34:07.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 23:34:07.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 23:34:07.214 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 23:34:07.215 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 23:34:07.227 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-31 23:34:07.237 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-31 23:34:07.237 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-31 23:34:07.302 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-31 23:34:07.357 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-08-31 23:34:07.357 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-08-31 23:34:07.361 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-31 23:34:07.362 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-31 23:34:07.363 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-08-31 23:34:07.364 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-08-31 23:34:07.367 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-31 23:34:07.368 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-08-31 23:34:07.369 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6931 | 🔧 [P1-2修复] 发现 6 个表的配置
2025-08-31 23:34:07.378 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: salary_data_2025_07_active_employees, 10个字段
2025-08-31 23:34:07.380 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:488 | 🔧 [P1-1修复] 加载统一格式映射: active_employees, 5个字段
2025-08-31 23:34:07.382 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_全部在职人员工资表, 30个字段
2025-08-31 23:34:07.383 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_退休人员工资表, 30个字段
2025-08-31 23:34:07.384 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_A岗职工, 30个字段
2025-08-31 23:34:07.385 | INFO     | src.modules.data_import.config_sync_manager:load_mapping:483 | 🔧 [P1-1修复] 加载旧格式映射: change_data_2025_12_离休人员工资表, 30个字段
2025-08-31 23:34:07.392 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6941 | ✅ [P1-2修复] 已加载字段映射信息，共6个表的映射
2025-08-31 23:34:07.405 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 23:34:07.409 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 23:34:07.411 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 23:34:07.412 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 23:34:07.413 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 23:34:07.414 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-08-31 23:34:07.415 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 23:34:07.415 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 23:34:07.417 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 23:34:07.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 23:34:07.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 16.7ms
2025-08-31 23:34:07.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 23:34:07.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 23:34:07.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 23:34:07.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 23:34:07.457 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 23:34:07.458 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-31 23:34:07.459 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8633 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-31 23:34:07.460 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-31 23:34:07.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-31 23:34:07.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-31 23:34:07.486 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-31 23:34:07.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-08-31 23:34:07.504 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-31 23:34:07.510 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-31 23:34:07.511 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-31 23:34:07.512 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-31 23:34:07.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 27.6ms
2025-08-31 23:34:07.514 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-31 23:34:07.515 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-31 23:34:07.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-08-31 23:34:07.522 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-31 23:34:07.522 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-31 23:34:07.524 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8651 | 已显示标准空表格，表头数量: 22
2025-08-31 23:34:07.525 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-08-31 23:34:07.585 | INFO     | __main__:main:514 | 应用程序启动成功
2025-08-31 23:34:07.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 23:34:07.637 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-08-31 23:34:07.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 23:34:07.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-31 23:34:07.758 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-31 23:34:07.762 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-08-31 23:34:07.815 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 23:34:07.818 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 23:34:07.820 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 23:34:08.266 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9535 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-31 23:34:08.267 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9445 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-31 23:34:08.272 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9459 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-31 23:34:08.272 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9993 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-31 23:34:08.293 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9465 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-31 23:34:08.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-31 23:34:08.822 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 23:34:08.828 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 23:34:08.828 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 23:34:09.830 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-08-31 23:34:09.834 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-31 23:34:09.835 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2173 | 未找到有效的工资数据表
2025-08-31 23:34:09.836 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1612 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-31 23:34:33.194 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-31 23:34:33.195 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8456 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-31 23:34:33.199 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 初始化统一数据导入窗口
2025-08-31 23:34:33.280 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 23:34:33.283 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-31 23:34:33.284 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-31 23:34:33.285 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-31 23:34:33.286 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-31 23:34:33.293 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-31 23:34:33.296 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-31 23:34:33.298 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 23:34:33.304 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-31 23:34:33.305 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-31 23:34:33.311 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-08-31 23:34:33.312 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-08-31 23:34:33.312 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-08-31 23:34:33.314 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-08-31 23:34:33 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-08-31 23:34:33 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-08-31 23:34:33 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-08-31 23:34:33 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-08-31 23:34:33.396 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 统一导入管理器初始化完成（包含第二阶段功能）
2025-08-31 23:34:33.400 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 核心组件初始化成功
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 接收到高级配置变化: {'file_import': {'default_import_path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'supported_formats': ['xlsx', 'xls', 'csv'], 'max_file_size_mb': 100, 'auto_detect_encoding': True, 'sheet_selection_strategy': 'all', 'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}, 'field_mapping': {'mapping_algorithm': 'fuzzy_match', 'similarity_threshold': 80, 'auto_mapping_enabled': True, 'required_field_check': True, 'field_type_validation': True, 'save_mapping_history': True}, 'smart_recommendations': {'confidence_threshold': 70, 'enable_history_learning': True, 'enable_semantic_analysis': True, 'auto_apply_high_confidence': False, 'template_priority': 0, 'max_saved_templates': 50}, 'data_processing': {'strict_validation': False, 'null_value_strategy': 0, 'auto_type_conversion': True, 'duplicate_strategy': 0, 'batch_size': 1000, 'error_tolerance': 10, 'remove_duplicates': False, 'handle_missing_values': 'keep', 'format_numbers': True, 'format_dates': True, 'trim_whitespace': True, 'convert_data_types': True, 'data_validation': True, 'custom_rules': [], 'saved_templates': []}, 'ui_customization': {'table_row_limit': 200, 'show_detailed_logs': False, 'show_confidence_indicators': True, 'auto_save_interval': 5, 'show_confirmation_dialogs': True, 'show_shortcuts': True}, 'performance': {'max_memory_usage': 2048, 'enable_caching': True, 'preload_data': False, 'thread_count': 4, 'enable_async_processing': True, 'progress_update_frequency': 100}}
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 应用默认导入文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - UI未初始化，保存默认路径供后续应用: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 文件导入配置已应用，包括Excel表结构设置: {'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 字段映射配置已应用
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 智能推荐配置已应用
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 数据处理配置已应用
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 界面个性化配置已应用
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 性能优化配置已应用
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 所有高级配置域已成功应用: ['file_import', 'field_mapping', 'smart_recommendations', 'data_processing', 'ui_customization', 'performance']
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 🔧 [P0修复] 已加载保存的高级配置
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 工作线程初始化完成
2025-08-31 23:34:33.423 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-08-31 23:34:33 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-08-31 23:34:33 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-08-31 23:34:33 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-08-31 23:34:33 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-08-31 23:34:33 - src.gui.performance_optimizer - INFO - 性能优化器初始化完成
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - UI界面创建完成
2025-08-31 23:34:33 - src.gui.unified_data_import_window - INFO - 应用待处理的默认文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:33.834 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-31 23:34:33.835 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:34:33.838 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:33.838 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:33.954 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 23:34:33.958 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:33.961 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:34:33.961 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-31 23:34:33.964 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:34:33.969 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-31 23:34:33.972 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-31 23:34:33.973 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:34.089 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:34.092 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-31 23:34:34.095 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:34.205 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:34.207 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 23:34:34.212 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:34.322 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:34.324 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 已加载 4 个Sheet
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 成功加载 4 个工作表
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 待处理配置应用完成: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 信号连接完成
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 统一数据导入窗口初始化完成
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 响应式布局初始化完成
2025-08-31 23:34:34 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-08-31 23:34:36 - src.gui.unified_data_import_window - INFO - 开始打开高级配置对话框...
2025-08-31 23:34:36 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 导入成功，开始初始化...
2025-08-31 23:34:36 - src.gui.advanced_config_dialog - INFO - 高级配置对话框开始初始化...
2025-08-31 23:34:36 - src.gui.advanced_config_dialog - INFO - 开始初始化UI...
2025-08-31 23:34:36 - src.gui.advanced_config_dialog - INFO - 开始加载配置...
2025-08-31 23:34:36 - src.gui.advanced_config_dialog - INFO - 高级配置加载成功
2025-08-31 23:34:36 - src.gui.advanced_config_dialog - INFO - 开始连接信号...
2025-08-31 23:34:36 - src.gui.advanced_config_dialog - INFO - 高级配置对话框初始化完成
2025-08-31 23:34:36 - src.gui.unified_data_import_window - INFO - AdvancedConfigDialog 初始化完成
2025-08-31 23:34:36 - src.gui.unified_data_import_window - INFO - config_changed 信号连接成功
2025-08-31 23:34:36 - src.gui.unified_data_import_window - INFO - 准备显示高级配置对话框...
2025-08-31 23:34:44 - src.gui.unified_data_import_window - INFO - 高级配置对话框关闭，返回值: 0
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-08-31 23:34:46.275 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: A岗职工
2025-08-31 23:34:46.278 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-08-31 23:34:46.294 | INFO     | src.modules.data_import.config_template_manager:_load_templates:579 | 加载了 10 个模板
2025-08-31 23:34:46.297 | INFO     | src.modules.data_import.config_template_manager:_init_builtin_templates:184 | 初始化了 4 个内置模板
2025-08-31 23:34:46.298 | INFO     | src.modules.data_import.config_template_manager:__init__:100 | 配置模板管理器初始化完成，模板目录: state\config_templates
2025-08-31 23:34:46.301 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:34:46.302 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: A岗职工
2025-08-31 23:34:46.306 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:34:46.308 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:46.309 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:46.421 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-08-31 23:34:46.425 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:46.427 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 23:34:46.428 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-08-31 23:34:46.433 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 21 个
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 21 行
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-08-31 23:34:46.475 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:46.585 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:46.587 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 23:34:46.590 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:34:46.592 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: A岗职工 - 62 行
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-08-31 23:34:46 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 选中Sheet: 全部在职人员工资表
2025-08-31 23:34:48.122 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '全部在职人员工资表' 创建默认配置
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 全部在职人员工资表
2025-08-31 23:34:48.126 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '全部在职人员工资表' 创建默认配置
2025-08-31 23:34:48.128 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:34:48.129 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 全部在职人员工资表
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 全部在职人员工资表
2025-08-31 23:34:48.131 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:34:48.131 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:48.132 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:48.240 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:48.242 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 23:34:48.243 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 23列
2025-08-31 23:34:48.247 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 23列
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 23 个
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 23 个字段, 表类型: 
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 23 行
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 全部在职人员工资表
2025-08-31 23:34:48.286 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:48.406 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:48.409 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 全部在职人员工资表 - 100 行
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 100 行, 23 列
2025-08-31 23:34:48 - src.gui.unified_data_import_window - INFO - 已加载Sheet '全部在职人员工资表' 的预览数据: 100 行
2025-08-31 23:34:56 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-08-31 23:34:56.851 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-08-31 23:34:56 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 离休人员工资表
2025-08-31 23:34:56.855 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-08-31 23:34:56.855 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:34:56.857 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-08-31 23:34:56 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 离休人员工资表
2025-08-31 23:34:56.859 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:34:56.859 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:56.860 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:56.970 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 23:34:56.974 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:56.976 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:34:56.976 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-08-31 23:34:56.979 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-08-31 23:34:56 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 16 个
2025-08-31 23:34:56 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-08-31 23:34:57 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-08-31 23:34:57 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-08-31 23:34:57.010 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:57.113 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:57.115 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:34:57.118 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:34:57.118 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-31 23:34:57 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 离休人员工资表 - 2 行
2025-08-31 23:34:57 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-08-31 23:34:57 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-08-31 23:34:59 - src.gui.unified_data_import_window - INFO - 选中Sheet: 退休人员工资表
2025-08-31 23:34:59.834 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '退休人员工资表' 创建默认配置
2025-08-31 23:34:59 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 退休人员工资表
2025-08-31 23:34:59.838 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '退休人员工资表' 创建默认配置
2025-08-31 23:34:59.839 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:34:59.840 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 退休人员工资表
2025-08-31 23:34:59 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 退休人员工资表
2025-08-31 23:34:59.843 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:34:59.844 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:34:59.844 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:34:59.959 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-08-31 23:34:59.964 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:34:59.966 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-31 23:34:59.967 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 27列
2025-08-31 23:34:59.970 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 27列
2025-08-31 23:34:59 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 27 个
2025-08-31 23:34:59 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 27 个字段, 表类型: 
2025-08-31 23:35:00 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 27 行
2025-08-31 23:35:00 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 退休人员工资表
2025-08-31 23:35:00.020 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:00.129 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:00.131 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-31 23:35:00.135 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:35:00.136 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-31 23:35:00 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 退休人员工资表 - 13 行
2025-08-31 23:35:00 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 13 行, 27 列
2025-08-31 23:35:00 - src.gui.unified_data_import_window - INFO - 已加载Sheet '退休人员工资表' 的预览数据: 13 行
2025-08-31 23:35:12 - src.gui.unified_data_import_window - INFO - 选中Sheet: 全部在职人员工资表
2025-08-31 23:35:12 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 全部在职人员工资表
2025-08-31 23:35:12.911 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:35:12.911 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 全部在职人员工资表
2025-08-31 23:35:12 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 全部在职人员工资表
2025-08-31 23:35:12.913 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:35:12.914 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:35:12.914 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:13.023 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-08-31 23:35:13.027 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:13.029 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 23:35:13.029 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 23列
2025-08-31 23:35:13.033 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 23列
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 23 个
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 23 个字段, 表类型: 
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 23 行
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 全部在职人员工资表
2025-08-31 23:35:13.076 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:13.183 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:13.185 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 全部在职人员工资表 - 100 行
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 100 行, 23 列
2025-08-31 23:35:13 - src.gui.unified_data_import_window - INFO - 已加载Sheet '全部在职人员工资表' 的预览数据: 100 行
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 离休人员工资表
2025-08-31 23:35:25.503 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:35:25.503 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 离休人员工资表
2025-08-31 23:35:25.505 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:35:25.506 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:35:25.507 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:25.619 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 23:35:25.623 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:25.625 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:35:25.626 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-08-31 23:35:25.629 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 16 个
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-08-31 23:35:25.670 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:25.796 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:25.800 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:35:25.804 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:35:25.805 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 离休人员工资表 - 2 行
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-08-31 23:35:25 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-08-31 23:35:28 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-08-31 23:35:28 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: A岗职工
2025-08-31 23:35:28.990 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:35:28.991 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-08-31 23:35:28 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: A岗职工
2025-08-31 23:35:28.993 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:35:28.993 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:35:28.994 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:29.099 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-08-31 23:35:29.104 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:29.105 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 23:35:29.106 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-08-31 23:35:29.109 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 21 个
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 21 个字段, 表类型: 
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 21 行
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-08-31 23:35:29.148 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:29.326 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:29.328 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-31 23:35:29.332 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:35:29.333 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: A岗职工 - 62 行
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-08-31 23:35:29 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 当前Sheet变化: 离休人员工资表
2025-08-31 23:35:39.574 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-08-31 23:35:39.575 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 更新字段映射配置: 离休人员工资表
2025-08-31 23:35:39.577 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-31 23:35:39.578 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-31 23:35:39.578 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:39.680 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-31 23:35:39.684 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:39.686 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:35:39.687 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-08-31 23:35:39.690 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 从Excel文件获取字段: 16 个
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 加载Excel字段: 16 个字段, 表类型: 
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 字段映射表格创建完成: 16 行
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-08-31 23:35:39.727 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-31 23:35:39.839 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-31 23:35:39.840 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-31 23:35:39.843 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-31 23:35:39.844 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 预览数据加载完成: 离休人员工资表 - 2 行
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-08-31 23:35:39 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-08-31 23:38:23.110 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6005 | 用户取消了数据导入
2025-08-31 23:38:24.997 | INFO     | __main__:main:519 | 应用程序正常退出
